"""
Main Flask Application for Google Form AutoFill Web Interface

This module initializes the Flask application and configures it.
"""

import os
import sys
from pathlib import Path
from flask import Flask
from dotenv import load_dotenv

# Add parent directory to path to import modules from parent directory
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Load environment variables from .env file if it exists
env_path = Path('.env')
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Create Flask application
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'dev-key-for-development-only')
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload size

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Import routes after app is created to avoid circular imports
# Use relative imports since we're in the web package
from .routes import register_routes
from .api import register_api_routes
from .customer_api import register_customer_api_routes

# Register routes
register_routes(app)
register_api_routes(app)  # API routes are now registered
register_customer_api_routes(app)  # Customer API routes

# Add template filters
@app.template_filter('timestamp_to_date')
def timestamp_to_date(timestamp):
    """Convert a timestamp to a formatted date string"""
    import datetime
    return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

if __name__ == '__main__':
    # Run the application in debug mode if not in production
    debug = os.environ.get('FLASK_ENV', 'development') == 'development'
    app.run(debug=debug, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
