{% extends "base.html" %}

{% block title %}Review Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    .review-card {
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .review-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .option-distribution {
        margin-top: 1rem;
    }
    
    .option-bar {
        height: 2rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }
    
    .option-progress {
        height: 100%;
        background-color: #007bff;
        text-align: right;
        color: white;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 0.5rem;
        border-radius: 0.25rem;
    }
    
    .option-label {
        margin-bottom: 0.25rem;
        display: flex;
        justify-content: space-between;
    }
    
    .example-response {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
    }
    
    .summary-box {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .summary-item {
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
    }
    
    .summary-label {
        font-weight: bold;
    }
    
    .summary-value {
        text-align: right;
    }
    
    .action-buttons {
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>Review Configuration
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ form_data.form_title }}</h5>
                        {% if form_data.form_description %}
                        <p>{{ form_data.form_description }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('customer_form', form_id=form_id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-edit me-1"></i>Edit Configuration
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="summary-box">
            <h5><i class="fas fa-chart-pie me-2"></i>Configuration Summary</h5>
            <div class="summary-item">
                <div class="summary-label">Total Responses Needed:</div>
                <div class="summary-value">{{ form_data.customer_config.total_responses }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Questions Configured:</div>
                <div class="summary-value">{{ form_data.customer_config.questions|length }} / {{ form_data.questions|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Configuration Date:</div>
                <div class="summary-value">{{ form_data.customer_config.created_at|int|timestamp_to_date }}</div>
            </div>
        </div>

        <h4 class="mb-4">Question Configuration Details</h4>
        
        {% for question in form_data.questions %}
        {% set question_id = question.id|string %}
        {% set question_config = form_data.customer_config.questions.get(question_id, {}) %}
        
        <div class="card review-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                        {% if question.required %}Required{% else %}Optional{% endif %}
                    </span>
                    {{ question.title }}
                    <span class="badge bg-info ms-2">{{ question.type_name }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if question.description %}
                <p class="text-muted mb-3">{{ question.description }}</p>
                {% endif %}
                
                {% if question_config %}
                    {% if question.type in ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating'] %}
                        <!-- Option-based questions -->
                        <div class="option-distribution">
                            <h6>Response Distribution</h6>
                            
                            {% for option in question.options %}
                                {% set percentage = question_config.options.get(option, 0) %}
                                <div class="option-label">
                                    <span>{{ option }}</span>
                                    <span>{{ percentage }}%</span>
                                </div>
                                <div class="option-bar">
                                    <div class="option-progress" style="width: {{ percentage }}%">
                                        {% if percentage > 10 %}{{ percentage }}%{% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Text-based questions -->
                        <div class="text-response-config">
                            {% if question_config.description %}
                            <h6>Response Guidelines</h6>
                            <p>{{ question_config.description }}</p>
                            {% endif %}
                            
                            {% if question_config.examples %}
                            <h6>Example Responses</h6>
                            {% for example in question_config.examples %}
                            <div class="example-response">
                                {{ example }}
                            </div>
                            {% endfor %}
                            {% endif %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This question has not been configured.
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
        
        <div class="action-buttons d-grid gap-2 d-md-flex justify-content-md-end mt-4 mb-5">
            <a href="{{ url_for('customer_form', form_id=form_id) }}" class="btn btn-secondary me-md-2">
                <i class="fas fa-edit me-1"></i>Edit Configuration
            </a>
            <a href="{{ url_for('customer_share', form_id=form_id) }}" class="btn btn-success">
                <i class="fas fa-share-alt me-1"></i>Generate Shareable Link
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add a filter to format timestamps
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleString();
    }
</script>
{% endblock %}
